export default function NineBot() {
  return (
    <svg
      width="112"
      height="24"
      viewBox="0 0 112 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_58883_46508)">
        <path
          d="M12 0C5.37718 0 0 5.37718 0 12C0 18.6228 5.37718 24 12 24C18.6228 24 24 18.6228 24 12C24 5.37718 18.6228 0 12 0ZM4.14388 11.1367C4.14388 10.7914 4.42754 10.5077 4.77287 10.5077H12.5427C12.888 10.5077 13.1716 10.7914 13.1716 11.1367C13.1716 11.482 12.888 11.7533 12.5427 11.7533H4.77287C4.42754 11.7533 4.14388 11.482 4.14388 11.1367ZM15.8602 12.1727C15.8602 12.1727 14.0349 18.0185 13.665 19.1655C13.147 20.7934 11.3587 21.5334 9.85406 20.8551C8.3371 20.1644 8.5961 18.7338 8.73176 18.3145L9.50874 15.8109C9.7184 15.1326 9.78006 14.7503 9.47174 14.2693C9.12641 13.7759 8.47276 13.8006 8.23844 13.8006H7.33813C6.99281 13.8006 6.72148 13.5293 6.72148 13.184C6.72148 12.8386 7.00514 12.555 7.33813 12.555H11.0134C11.852 12.555 12.2713 13.0236 11.8767 13.8006L9.90339 17.6978C9.52107 18.4378 9.50874 19.3751 10.3597 19.7204C11.408 20.1644 12.2837 19.3998 12.6043 18.4008C12.7523 17.9322 14.6763 11.778 14.6763 11.778C14.8489 11.2477 14.9846 10.6187 14.6516 10.1377C14.3309 9.69373 13.6403 9.66906 13.406 9.66906H6.46249C6.11716 9.66906 5.84584 9.38541 5.84584 9.04008C5.84584 8.69476 6.1295 8.4111 6.46249 8.4111H13.406C13.6773 8.4111 14.8983 8.38643 15.6382 9.32374C16.1809 10.0514 16.2425 10.9764 15.8602 12.1727ZM15.2559 7.81912C14.0966 7.81912 13.1593 6.88181 13.1593 5.72251C13.1593 4.56321 14.0966 3.6259 15.2559 3.6259C16.4152 3.6259 17.3525 4.56321 17.3525 5.72251C17.3525 6.88181 16.4152 7.81912 15.2559 7.81912Z"
          fill="black"
        />
        <path
          d="M35.0497 6.93081C33.3231 7.05414 32.9777 7.68312 31.1895 7.59679V18.3265H33.5204V9.85373C33.5204 9.21241 34.063 8.90409 34.729 8.90409C35.395 8.90409 35.9377 9.21241 35.9377 9.85373V18.3388H38.2686V9.95239C38.2933 7.97911 37.0353 6.78281 35.0497 6.93081ZM39.6746 4.45188V6.44982L41.4752 6.0305C41.7835 5.9565 42.0055 5.68518 42.0055 5.36452V3.37891L40.2049 3.78589C39.8965 3.85989 39.6746 4.13122 39.6746 4.45188ZM39.6746 8.4231V18.3388H42.0055V7.3378L40.2049 7.75712C39.8965 7.83112 39.6746 8.10244 39.6746 8.4231ZM79.5348 6.19083V4.78487L77.7342 5.20419C77.4258 5.27819 77.2038 5.54951 77.2038 5.87017V8.37377V15.3912C77.1915 17.3522 78.7825 18.3265 80.7434 18.3265H81.915V16.3532H80.7434C80.0774 16.3532 79.5348 16.0449 79.5348 15.4036V9.55773L81.915 9.01508V7.00481L79.5348 7.54746V6.19083ZM64.1802 6.91847C63.6376 6.94314 63.0826 7.10347 62.6632 7.32546V3.37891L60.8626 3.79823C60.5543 3.87223 60.3323 4.14355 60.3323 4.46421V17.7592C62.1206 17.6728 62.4536 18.3018 64.1925 18.4252C66.1658 18.5731 67.4238 17.3769 67.4114 15.4159C67.4114 14.0099 67.4114 11.198 67.4114 9.79206C67.4114 7.90511 66.1658 6.80748 64.1802 6.91847ZM65.0805 15.5146C65.0805 16.1559 64.5379 16.4642 63.8719 16.4642C63.2059 16.4642 62.6632 16.1559 62.6632 15.5146V9.84139C62.6632 9.20008 63.2059 8.89175 63.8719 8.89175C64.5379 8.89175 65.0805 9.20008 65.0805 9.84139V15.5146ZM72.3323 6.93081C70.3714 6.93081 68.7804 7.90511 68.7927 9.86606V15.5022C68.7804 17.4632 70.3714 18.4375 72.3323 18.4375C74.2933 18.4375 75.8842 17.4632 75.8719 15.5022V9.86606C75.8842 7.90511 74.2933 6.93081 72.3323 6.93081ZM73.5409 15.5146C73.5409 16.1559 72.9983 16.4642 72.3323 16.4642C71.6663 16.4642 71.1237 16.1559 71.1237 15.5146V9.84139C71.1237 9.20008 71.6663 8.89175 72.3323 8.89175C72.9983 8.89175 73.5409 9.20008 73.5409 9.84139V15.5146ZM46.951 6.93081C44.9901 6.93081 43.3991 7.90511 43.4114 9.86606V18.3265H45.7424V9.85373C45.7424 9.21241 46.285 8.90409 46.951 8.90409C47.617 8.90409 48.1596 9.21241 48.1596 9.85373V18.3388H50.4906V9.86606C50.5029 7.90511 48.912 6.93081 46.951 6.93081ZM58.951 12.5917C58.951 12.5917 58.951 11.161 58.951 9.86606C58.951 7.90511 57.3724 6.93081 55.4114 6.93081C53.4505 6.93081 51.8595 7.90511 51.8719 9.86606V15.5022C51.8595 17.4632 53.4505 18.4375 55.4114 18.4375C57.3724 18.4375 58.9633 17.4632 58.951 15.5022V13.9976L56.6201 14.5403V15.5146C56.6201 16.1559 56.0774 16.4642 55.4114 16.4642C54.7455 16.4642 54.2028 16.1559 54.2028 15.5146V13.6893L58.951 12.5917ZM54.2028 9.85373C54.2028 9.21241 54.7455 8.90409 55.4114 8.90409C56.0774 8.90409 56.6201 9.21241 56.6201 9.85373C56.6201 10.1744 56.6201 11.1117 56.6201 11.1117L54.2028 11.6667C54.2028 11.6667 54.2028 10.31 54.2028 9.85373ZM106.754 8.04078H102.499V6.59782H107.728V9.57007H110.552V5.05619H99.6746V9.57007H106.754V8.04078ZM112.168 12.0367V10.5074H98.0343V12.0367H100.119L99.5512 14.5156H107.444C106.655 16.0942 105.323 16.7972 104.657 17.0315L107.469 18.6718C107.506 18.6471 110.478 17.3152 110.651 12.9863H102.943L103.165 12.049H112.168V12.0367ZM96.283 15.4036V7.02947H90.4618C90.4741 6.28949 90.4865 5.54951 90.4865 4.80953H87.6622C87.6622 5.58651 87.6622 6.32649 87.6499 7.02947H84.9983V8.73142H87.5882C87.3909 12.2587 86.6509 14.9596 84.283 16.8835L87.1566 18.5485C89.5492 15.9339 90.1782 12.3573 90.3878 8.74376H93.4587V16.1189C93.4587 18.9555 98.0219 18.4128 98.0219 18.4128L98.6139 15.8599C98.6139 15.8476 96.283 16.6985 96.283 15.4036Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_58883_46508">
          <rect width="112" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
